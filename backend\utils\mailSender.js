const nodemailer = require('nodemailer');

const mailSender = async (email, title, body) => {
    try {
        // Check if mail configuration exists
        if (!process.env.MAIL_HOST || !process.env.MAIL_USER || !process.env.MAIL_PASS) {
            console.log('Mail configuration missing');
            // Return a dummy success response in development
            return {
                response: 'Mail configuration missing, but continuing in development mode',
                accepted: [email]
            };
        }

        const transporter = nodemailer.createTransport({
            host: process.env.MAIL_HOST,
            auth: {
                user: process.env.MAIL_USER,
                pass: process.env.MAIL_PASS
            }
        });

        const info = await transporter.sendMail({
            from: '<PERSON>Notion || by <PERSON><PERSON><PERSON><PERSON>',
            to: email,
            subject: title,
            html: body
        });

        console.log('Email sent successfully to: ', email);
        return info;
    }
    catch (error) {
        console.log('Error while sending mail to: ', email);
        console.error(error);
        // Return a dummy success response in development
        return {
            response: 'Error sending mail, but continuing in development mode',
            accepted: [email]
        };
    }
}

module.exports = mailSender;