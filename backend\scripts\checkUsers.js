const mongoose = require('mongoose');
const User = require('../models/user');
require('dotenv').config();

async function checkUsers() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Database connected successfully');

        // Find all users
        const users = await User.find({});
        console.log('Users found:', users.length);
        
        // Print each user's email
        users.forEach(user => {
            console.log('User email:', user.email);
        });

        process.exit(0);
    } catch (error) {
        console.error('Error checking users:', error);
        process.exit(1);
    }
}

checkUsers();
