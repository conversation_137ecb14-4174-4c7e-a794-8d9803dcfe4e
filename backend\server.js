const express = require('express')
const app = express();

// packages
const fileUpload = require('express-fileupload');
const cookieParser = require('cookie-parser');
const cors = require('cors');
require('dotenv').config();

// connection to DB and cloudinary
const { connectDB } = require('./config/database');
const { cloudinaryConnect } = require('./config/cloudinary');

// routes
const userRoutes = require('./routes/user');
const profileRoutes = require('./routes/profile');
const paymentRoutes = require('./routes/payments');
const courseRoutes = require('./routes/course');


// middleware
app.use(express.json()); // to parse json body
app.use(cookieParser());
app.use(
    cors({
        origin: 'http://localhost:5173', // Specify the exact origin
        credentials: true
    })
);
app.use(
    fileUpload({
        useTempFiles: true,
        tempFileDir: '/tmp'
    })
)


const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
    console.log(`Server Started on PORT ${PORT}`);
});

// connections
connectDB();
cloudinaryConnect();

// mount route
app.use('/api/v1/auth', userRoutes);
app.use('/api/v1/profile', profileRoutes);
app.use('/api/v1/payment', paymentRoutes);
app.use('/api/v1/course', courseRoutes);




// Default Route
app.get('/', (req, res) => {
    console.log('Default route accessed');
    res.send(`<div>
    This is Default Route
    <p>Everything is OK</p>
    </div>`);
});

// Test Route
app.get('/test', (req, res) => {
    console.log('Test route accessed');
    res.json({ success: true, message: 'Test route working' });
});

// Test Login Route
app.post('/test-login', (req, res) => {
    console.log('Test login route accessed with body:', req.body);
    res.json({ success: true, message: 'Test login route working', data: req.body });
})