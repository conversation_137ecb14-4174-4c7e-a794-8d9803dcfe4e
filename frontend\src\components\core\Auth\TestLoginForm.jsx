import { useState } from "react";
import axios from "axios";

function TestLoginForm() {
  const [formData, setFormData] = useState({
    email: "<EMAIL>",
    password: "kalyani7678",
  });

  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);

  const handleOnChange = (e) => {
    setFormData((prevData) => ({
      ...prevData,
      [e.target.name]: e.target.value,
    }));
  };

  const handleOnSubmit = async (e) => {
    e.preventDefault();
    try {
      // First try the test login endpoint
      console.log("Sending test login request to:", "http://localhost:5000/test-login");
      const testResponse = await axios.post("http://localhost:5000/test-login", formData, {
        withCredentials: true,
      });
      console.log("Test login response:", testResponse.data);
      setResponse(testResponse.data);
      
      // Then try the actual login endpoint
      console.log("Sending login request to:", "http://localhost:5000/api/v1/auth/login");
      const loginResponse = await axios.post("http://localhost:5000/api/v1/auth/login", formData, {
        withCredentials: true,
      });
      console.log("Login response:", loginResponse.data);
      setResponse(loginResponse.data);
    } catch (error) {
      console.error("Error:", error);
      setError(error.message);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Test Login Form</h2>
      
      <form onSubmit={handleOnSubmit} className="space-y-4">
        <div>
          <label className="block text-gray-700">Email:</label>
          <input
            type="text"
            name="email"
            value={formData.email}
            onChange={handleOnChange}
            className="w-full px-4 py-2 border rounded-lg"
          />
        </div>
        
        <div>
          <label className="block text-gray-700">Password:</label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleOnChange}
            className="w-full px-4 py-2 border rounded-lg"
          />
        </div>
        
        <button
          type="submit"
          className="w-full py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          Test Login
        </button>
      </form>
      
      {response && (
        <div className="mt-6 p-4 bg-green-100 rounded-lg">
          <h3 className="font-bold text-green-800">Response:</h3>
          <pre className="mt-2 text-sm overflow-auto max-h-40">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
      
      {error && (
        <div className="mt-6 p-4 bg-red-100 rounded-lg">
          <h3 className="font-bold text-red-800">Error:</h3>
          <p className="mt-2 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
}

export default TestLoginForm;
