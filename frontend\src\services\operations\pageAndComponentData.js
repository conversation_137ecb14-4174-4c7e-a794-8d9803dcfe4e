import React from 'react'
// import { toast } from "react-hot-toast"
import { apiConnector } from '../apiConnector';
import { catalogData } from '../apis';


// ================ get Catalog Page Data  ================
export const getCatalogPageData = async (categoryId) => {
  // const toastId = toast.loading("Loading...");
  let result = [];
  try {
    console.log('Calling CATALOGPAGEDATA_API:', catalogData.CATALOGPAGEDATA_API)
    console.log('With categoryId:', categoryId)
    const response = await apiConnector("POST", catalogData.CATALOGPAGEDATA_API,
      { categoryId: categoryId, });

    console.log("CATALOG PAGE DATA API RESPONSE............", response)

    if (!response?.data?.success) {
      console.log('API response indicates failure:', response?.data)
      throw new Error("Could not Fetch Category page data");
    }

    console.log("CATALOG PAGE DATA:", response?.data?.data)
    result = response?.data?.data;

  }
  catch (error) {
    console.log("CATALOG PAGE DATA API ERROR....", error);
    // toast.error(error.response?.data.message);
    if (error.response?.data?.data) {
      result = error.response?.data.data;
    }
  }
  // toast.dismiss(toastId);
  return result;
}

