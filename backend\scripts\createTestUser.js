const mongoose = require('mongoose');
const User = require('../models/user');
const Profile = require('../models/profile');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function createTestUser() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Database connected successfully');

        // Create a profile
        const profileDetails = await Profile.create({
            gender: null, 
            dateOfBirth: null, 
            about: null, 
            contactNumber: null
        });
        console.log('Profile created:', profileDetails);

        // Hash the password
        const hashedPassword = await bcrypt.hash('password123', 10);

        // Create a user
        const userData = await User.create({
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            password: hashedPassword,
            accountType: 'Student',
            additionalDetails: profileDetails._id,
            approved: true,
            image: `https://api.dicebear.com/5.x/initials/svg?seed=Test User`
        });
        console.log('User created:', userData);

        console.log('Test user created successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error creating test user:', error);
        process.exit(1);
    }
}

createTestUser();
