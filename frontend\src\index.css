@tailwind base;
@tailwind components;
@tailwind utilities;



html,
body {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* use in homePage */
.homepage_bg {
  background: url('./assets/Images/bghome.svg');
}


.gradient_color {
  background: linear-gradient(118.19deg, #1FA2FF -3.62%, #12D8FA 50.44%, #A6FFCB 104.51%),
    linear-gradient(0deg, #F1F2FF, #F1F2FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}


/* ===== Scrollbar CSS ===== */
/* Firefox */
* {
  scrollbar-width: auto;
  scrollbar-color: #afb2bf;
}

/* Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 7px;
}

*::-webkit-scrollbar-track {
  background: #000814;
}

*::-webkit-scrollbar-thumb {
  background-color: #afb2bf;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #767986f5;
}


/* loading spinner  */
.custom-loader {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: conic-gradient(#0000 15%, #ffffff);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
  animation: s3 0.6s infinite linear;
}

@keyframes s3 {
  to {
    transform: rotate(1turn)
  }
}


.lable-style {
  @apply text-[14px] text-richblack-5;
}

.form-style {
  @apply rounded-lg bg-richblack-700 p-3 text-[16px] leading-[24px] text-richblack-5 shadow-[0_1px_0_0] shadow-white/30 placeholder:text-richblack-400 focus:outline-none;
}

.section_heading {
  @apply text-2xl font-bold text-richblack-5 lg:text-4xl;
}


/* Buttons */
.yellowButton {
  @apply cursor-pointer rounded-md bg-yellow-50 px-[20px] py-[8px] font-semibold text-richblack-900 hover:bg-black hover:text-yellow-50 duration-300;
}

.blackButton {
  @apply cursor-pointer rounded-md bg-richblack-800 px-[20px] py-[8px] font-semibold text-richblack-5 hover:bg-richblack-5 hover:text-richblack-800 duration-300;
}





/* Loading skeleton */
.skeleton {
  position: relative;
  overflow: hidden;
  background-color: #0a2955;
  background-color: rgb(44 51 63 / 1);
}

.skeleton::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(90deg,
      rgba(111, 119, 131, 0) 0,
      rgba(143, 132, 132, 0) 20%,
      rgba(126, 139, 151, 0.5) 60%,
      rgba(25, 55, 99, 0));
  animation: shimmer 0.9s infinite;
  content: "";
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}


/* used in Navbar */
.top {
  /* background: rgba(0, 0, 0, 0.25); */
  background: #222831a1;
  backdrop-filter: blur(3.5px);
  -webkit-backdrop-filter: blur(3.5px);
}

.show {
  background-color: #020c1b;
}

.hide {
  transform: translateY(-60px);
}


.glass-bg {
  backdrop-filter: blur(9px) saturate(200%);
  -webkit-backdrop-filter: blur(9px) saturate(200%);
  background-color: rgba(17, 25, 40, 0.84);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.125);

  background-color: #111927;
}



/* used in code block component for background  */
.code-block1-grad {
  position: absolute;
  width: 372.95px;
  height: 257.05px;
  left: calc(50% - 372.95px/2 - 16.53px);
  top: calc(50% - 257.05px/2 - 17.47px);

  background: linear-gradient(123.77deg, #8A2BE2 -6.46%, #FFA500 59.04%, #F8F8FF 124.53%);
  opacity: 0.2;
  filter: blur(34px);
  transform: matrix(1, 0, -0.03, 1, 0, 0);

  flex: none;
  order: 0;
  flex-grow: 0;
  z-index: 0;
}


/* code block 2 bg gradient */
.code-block2-grad {
  position: absolute;
  width: 372.95px;
  height: 257.05px;
  left: calc(50% - 372.95px/2 - 26.53px);
  top: calc(50% - 257.05px/2 - 17.47px);


  background: linear-gradient(118.19deg, #1FA2FF -3.62%, #12D8FA 50.44%, #A6FFCB 104.51%);
  opacity: 0.2;
  filter: blur(34px);
  transform: matrix(1, 0, -0.03, 1, 0, 0);

  flex: none;
  order: 0;
  flex-grow: 0;
  z-index: 0;
}

/* code block card glass background */
.glass {
  background: rgba(255, 255, 255, 0);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.14);
}




.code-border {
  background: linear-gradient(111.93deg,
      rgba(14, 26, 45, 0.24) -1.4%,
      rgba(17, 30, 50, 0.38) 104.96%)
}


/* used in home.jsx - random background  */
.opacity_layer_bg {
  background: linear-gradient(180deg, rgba(4, 21, 45, 0) 0%, #04152d 79.17%);
}

