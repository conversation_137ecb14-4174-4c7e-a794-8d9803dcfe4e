const mongoose = require('mongoose');
const Category = require('../models/category');
require('dotenv').config();

async function checkCategories() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Database connected successfully');

        // Find all categories
        const categories = await Category.find({});
        console.log('Categories found:', categories.length);
        
        // Print each category
        categories.forEach(category => {
            console.log('Category:', {
                id: category._id,
                name: category.name,
                description: category.description,
                courses: category.courses.length
            });
        });

        process.exit(0);
    } catch (error) {
        console.error('Error checking categories:', error);
        process.exit(1);
    }
}

checkCategories();
