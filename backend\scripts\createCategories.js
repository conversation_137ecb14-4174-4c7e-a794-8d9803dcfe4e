const mongoose = require('mongoose');
const Category = require('../models/category');
require('dotenv').config();

async function createCategories() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Database connected successfully');

        // Define categories
        const categories = [
            {
                name: 'Web Development',
                description: 'Learn how to build websites and web applications using HTML, CSS, JavaScript, and various frameworks.'
            },
            {
                name: 'Mobile Development',
                description: 'Learn how to build mobile applications for iOS and Android using React Native, Flutter, and other frameworks.'
            },
            {
                name: 'Data Science',
                description: 'Learn how to analyze and visualize data using Python, R, and various data science tools.'
            },
            {
                name: 'Machine Learning',
                description: 'Learn how to build machine learning models using Python, TensorFlow, PyTorch, and other frameworks.'
            },
            {
                name: 'Cloud Computing',
                description: 'Learn how to deploy and manage applications on cloud platforms like AWS, Azure, and Google Cloud.'
            }
        ];

        // Create categories
        const createdCategories = await Category.insertMany(categories);
        console.log('Categories created:', createdCategories.length);
        
        // Print each created category
        createdCategories.forEach(category => {
            console.log('Category created:', {
                id: category._id,
                name: category.name,
                description: category.description
            });
        });

        process.exit(0);
    } catch (error) {
        console.error('Error creating categories:', error);
        process.exit(1);
    }
}

createCategories();
