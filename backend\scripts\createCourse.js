const mongoose = require('mongoose');
const Course = require('../models/course');
const Category = require('../models/category');
const User = require('../models/user');
const Section = require('../models/section');
const SubSection = require('../models/subSection');
require('dotenv').config();

async function createCourse() {
    try {
        // Connect to the database
        await mongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Database connected successfully');

        // Find the Web Development category
        const webDevCategory = await Category.findOne({ name: 'Web Development' });
        if (!webDevCategory) {
            throw new Error('Web Development category not found');
        }
        console.log('Web Development category found:', webDevCategory._id);

        // Find an instructor user
        const instructor = await User.findOne({ accountType: 'Student' });
        if (!instructor) {
            throw new Error('No instructor found');
        }
        console.log('Instructor found:', instructor._id);

        // Create a section
        const section = await Section.create({
            sectionName: 'Introduction to HTML'
        });
        console.log('Section created:', section._id);

        // Create a subsection
        const subSection = await SubSection.create({
            title: 'HTML Basics',
            timeDuration: '00:10:00',
            description: 'Learn the basics of HTML',
            videoUrl: 'https://example.com/video.mp4'
        });
        console.log('SubSection created:', subSection._id);

        // Add the subsection to the section
        section.subSection.push(subSection._id);
        await section.save();
        console.log('SubSection added to Section');

        // Create a course
        const course = await Course.create({
            courseName: 'HTML and CSS Fundamentals',
            courseDescription: 'Learn the fundamentals of HTML and CSS to build beautiful websites',
            instructor: instructor._id,
            whatYouWillLearn: 'HTML, CSS, and basic web design principles',
            courseContent: [section._id],
            ratingAndReviews: [],
            price: 499,
            thumbnail: 'https://example.com/thumbnail.jpg',
            category: webDevCategory._id,
            tags: ['HTML', 'CSS', 'Web Development'],
            status: 'Published',
            instructions: 'No prior knowledge required',
            sold: 0
        });
        console.log('Course created:', course._id);

        // Add the course to the category
        webDevCategory.courses.push(course._id);
        await webDevCategory.save();
        console.log('Course added to Web Development category');

        // Create another category for testing
        const dataScience = await Category.findOne({ name: 'Data Science' });
        if (!dataScience) {
            throw new Error('Data Science category not found');
        }
        console.log('Data Science category found:', dataScience._id);

        // Create a course for Data Science
        const dataScienceCourse = await Course.create({
            courseName: 'Introduction to Data Science',
            courseDescription: 'Learn the fundamentals of data science and analysis',
            instructor: instructor._id,
            whatYouWillLearn: 'Python, pandas, numpy, and data visualization',
            courseContent: [],
            ratingAndReviews: [],
            price: 699,
            thumbnail: 'https://example.com/data-science-thumbnail.jpg',
            category: dataScience._id,
            tags: ['Python', 'Data Science', 'Machine Learning'],
            status: 'Published',
            instructions: 'Basic programming knowledge required',
            sold: 0
        });
        console.log('Data Science course created:', dataScienceCourse._id);

        // Add the course to the Data Science category
        dataScience.courses.push(dataScienceCourse._id);
        await dataScience.save();
        console.log('Course added to Data Science category');

        console.log('Courses created successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error creating course:', error);
        process.exit(1);
    }
}

createCourse();
