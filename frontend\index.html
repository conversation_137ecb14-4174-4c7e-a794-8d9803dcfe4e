<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/studyNotionLogo.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Study Notion</title>


  <!-- <link rel="preconnect" href="https://fonts.googleapis.com"> -->
  <!-- <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin> -->
  <link href="https://fonts.googleapis.com/css2?family=Boogaloo&display=swap" rel="stylesheet">
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>

  <!-- Error while using react-super-responsive-table -->
  <script>const global = globalThis;</script>
  <!-- <script>window.global = window;</script> -->
</body>

</html>