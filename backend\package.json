{"dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^1.40.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-random-string": "^5.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "node-schedule": "^2.1.1", "nodemailer": "^6.9.5", "nodemon": "^3.0.1", "otp-generator": "^4.0.1", "razorpay": "^2.9.2"}, "name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "build": "npm i"}, "keywords": [], "author": "", "license": "ISC", "description": ""}